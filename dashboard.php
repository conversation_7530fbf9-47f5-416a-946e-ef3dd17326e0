<?php
session_start();
include 'db.php';

if (!isset($_SESSION['user'])) {
    header("Location: login.php");
    exit();
}

$user = $_SESSION['user'];
$theme = $user['theme'] ?? 'light';
?>
<!DOCTYPE html>
<html>
<head>
    <title>Dashboard</title>
    <link rel="stylesheet" href="css/<?php echo $theme; ?>.css">
</head>
<body>
    <h1>Welcome, <?= htmlspecialchars($user['name']) ?>!</h1>
    <p>You are logged in as <?= $user['role'] ?>.</p>
    <label>Choose Theme: </label>
    <select id="themeSelect">
        <option value="light" <?= $theme == 'light' ? 'selected' : '' ?>>Light</option>
        <option value="dark" <?= $theme == 'dark' ? 'selected' : '' ?>>Dark</option>
        <option value="custom" <?= $theme == 'custom' ? 'selected' : '' ?>>Custom</option>
    </select>
    <script>
    document.getElementById('themeSelect').onchange = function() {
        fetch('update_theme.php', {
            method: 'POST',
            headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
            body: 'theme=' + this.value
        }).then(() => location.reload());
    };
    </script>
    <br><br>
    <a href="logout.php">Logout</a>
</body>
</html>