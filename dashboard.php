<?php
session_start();
include 'db.php';

if (!isset($_SESSION['user'])) {
    header("Location: login.php");
    exit();
}

$user = $_SESSION['user'];
$theme = $user['theme'] ?? 'light';

// Get user customization settings if they exist
$customizations = [];
if ($theme === 'custom') {
    $stmt = $conn->prepare("SELECT * FROM user_customizations WHERE user_id = ?");
    $stmt->bind_param("i", $user['id']);
    $stmt->execute();
    $result = $stmt->get_result();
    if ($row = $result->fetch_assoc()) {
        $customizations = $row;
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Glassmorphism Dashboard - <?= htmlspecialchars($user['name']) ?></title>
    <link rel="stylesheet" href="css/<?php echo $theme; ?>.css">
    <?php if ($theme === 'custom' && !empty($customizations)): ?>
    <style>
        :root {
            --user-primary-color: <?= $customizations['primary_color'] ?? '#ff6b6b' ?>;
            --user-secondary-color: <?= $customizations['secondary_color'] ?? '#ffa8a8' ?>;
            --user-blur-intensity: <?= $customizations['blur_intensity'] ?? '20' ?>px;
            --user-border-radius: <?= $customizations['border_radius'] ?? '20' ?>px;
            --user-animation-speed: <?= $customizations['animation_speed'] ?? '1' ?>;
        }
    </style>
    <?php endif; ?>
</head>
<body>
    <!-- Navigation -->
    <nav class="nav">
        <div class="nav-brand">✨ GlassShop</div>
        <div class="nav-links">
            <span>Welcome, <?= htmlspecialchars($user['name']) ?>!</span>
            <a href="logout.php" class="btn">Logout</a>
        </div>
    </nav>

    <!-- Main Dashboard Container -->
    <div class="glass-container">
        <h1>Dashboard</h1>
        <p>You are logged in as <strong><?= htmlspecialchars($user['role']) ?></strong></p>

        <!-- Theme Selector -->
        <div class="theme-selector">
            <div class="theme-option <?= $theme == 'light' ? 'active' : '' ?>" data-theme="light">
                <h3>☀️ Light</h3>
                <p>Clean & Bright</p>
            </div>
            <div class="theme-option <?= $theme == 'dark' ? 'active' : '' ?>" data-theme="dark">
                <h3>🌙 Dark</h3>
                <p>Elegant & Modern</p>
            </div>
            <div class="theme-option <?= $theme == 'custom' ? 'active' : '' ?>" data-theme="custom">
                <h3>🎨 Custom</h3>
                <p>Your Style</p>
            </div>
        </div>

        <!-- Custom Theme Customization Panel -->
        <?php if ($theme === 'custom'): ?>
        <div class="customization-panel">
            <h2>🎨 Customize Your Theme</h2>
            <p>Personalize your glassmorphism experience</p>

            <div class="color-picker-group">
                <div class="color-picker">
                    <label>Primary Color</label>
                    <input type="color" id="primaryColor" value="<?= $customizations['primary_color'] ?? '#ff6b6b' ?>">
                </div>
                <div class="color-picker">
                    <label>Secondary Color</label>
                    <input type="color" id="secondaryColor" value="<?= $customizations['secondary_color'] ?? '#ffa8a8' ?>">
                </div>
            </div>

            <div class="slider-group">
                <label>Blur Intensity: <span id="blurValue"><?= $customizations['blur_intensity'] ?? '20' ?>px</span></label>
                <input type="range" id="blurIntensity" min="5" max="50" value="<?= $customizations['blur_intensity'] ?? '20' ?>">
            </div>

            <div class="slider-group">
                <label>Border Radius: <span id="radiusValue"><?= $customizations['border_radius'] ?? '20' ?>px</span></label>
                <input type="range" id="borderRadius" min="5" max="50" value="<?= $customizations['border_radius'] ?? '20' ?>">
            </div>

            <div class="slider-group">
                <label>Animation Speed: <span id="speedValue"><?= $customizations['animation_speed'] ?? '1' ?>x</span></label>
                <input type="range" id="animationSpeed" min="0.5" max="3" step="0.1" value="<?= $customizations['animation_speed'] ?? '1' ?>">
            </div>

            <button class="btn btn-primary" onclick="saveCustomizations()">💾 Save Customizations</button>
            <button class="btn" onclick="resetCustomizations()">🔄 Reset to Default</button>
        </div>
        <?php endif; ?>

        <!-- Dashboard Content -->
        <div class="glass-container">
            <h2>📊 Dashboard Overview</h2>
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem; margin: 2rem 0;">
                <div class="glass-container" style="margin: 0; padding: 1.5rem;">
                    <h3>🛍️ Products</h3>
                    <p style="font-size: 2rem; font-weight: bold; margin: 0.5rem 0;">24</p>
                    <p style="margin: 0;">Total Items</p>
                </div>
                <div class="glass-container" style="margin: 0; padding: 1.5rem;">
                    <h3>👥 Customers</h3>
                    <p style="font-size: 2rem; font-weight: bold; margin: 0.5rem 0;">156</p>
                    <p style="margin: 0;">Active Users</p>
                </div>
                <div class="glass-container" style="margin: 0; padding: 1.5rem;">
                    <h3>💰 Sales</h3>
                    <p style="font-size: 2rem; font-weight: bold; margin: 0.5rem 0;">$12,450</p>
                    <p style="margin: 0;">This Month</p>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Theme switching functionality
        document.querySelectorAll('.theme-option').forEach(option => {
            option.addEventListener('click', function() {
                const theme = this.dataset.theme;

                fetch('update_theme.php', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
                    body: 'theme=' + theme
                }).then(() => location.reload());
            });
        });

        <?php if ($theme === 'custom'): ?>
        // Real-time customization updates
        function updateCustomProperty(property, value, suffix = '') {
            document.documentElement.style.setProperty(property, value + suffix);
        }

        // Color pickers
        document.getElementById('primaryColor').addEventListener('input', function() {
            updateCustomProperty('--user-primary-color', this.value);
        });

        document.getElementById('secondaryColor').addEventListener('input', function() {
            updateCustomProperty('--user-secondary-color', this.value);
        });

        // Sliders
        document.getElementById('blurIntensity').addEventListener('input', function() {
            updateCustomProperty('--user-blur-intensity', this.value, 'px');
            document.getElementById('blurValue').textContent = this.value + 'px';
        });

        document.getElementById('borderRadius').addEventListener('input', function() {
            updateCustomProperty('--user-border-radius', this.value, 'px');
            document.getElementById('radiusValue').textContent = this.value + 'px';
        });

        document.getElementById('animationSpeed').addEventListener('input', function() {
            updateCustomProperty('--user-animation-speed', this.value);
            document.getElementById('speedValue').textContent = this.value + 'x';
        });

        // Save customizations
        function saveCustomizations() {
            const customizations = {
                primary_color: document.getElementById('primaryColor').value,
                secondary_color: document.getElementById('secondaryColor').value,
                blur_intensity: document.getElementById('blurIntensity').value,
                border_radius: document.getElementById('borderRadius').value,
                animation_speed: document.getElementById('animationSpeed').value
            };

            fetch('save_customizations.php', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(customizations)
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('✅ Customizations saved successfully!');
                } else {
                    alert('❌ Error saving customizations: ' + data.message);
                }
            })
            .catch(error => {
                alert('❌ Error: ' + error.message);
            });
        }

        // Reset customizations
        function resetCustomizations() {
            document.getElementById('primaryColor').value = '#ff6b6b';
            document.getElementById('secondaryColor').value = '#ffa8a8';
            document.getElementById('blurIntensity').value = '20';
            document.getElementById('borderRadius').value = '20';
            document.getElementById('animationSpeed').value = '1';

            updateCustomProperty('--user-primary-color', '#ff6b6b');
            updateCustomProperty('--user-secondary-color', '#ffa8a8');
            updateCustomProperty('--user-blur-intensity', '20', 'px');
            updateCustomProperty('--user-border-radius', '20', 'px');
            updateCustomProperty('--user-animation-speed', '1');

            document.getElementById('blurValue').textContent = '20px';
            document.getElementById('radiusValue').textContent = '20px';
            document.getElementById('speedValue').textContent = '1x';
        }
        <?php endif; ?>
    </script>
</body>
</html>