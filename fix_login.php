<?php
// Quick fix for login issues
include 'db.php';

echo "<!DOCTYPE html><html><head><title>Login Fix</title>";
echo "<style>body{font-family:Arial,sans-serif;margin:20px;background:#f5f5f5;}";
echo ".container{max-width:600px;margin:0 auto;background:white;padding:20px;border-radius:10px;box-shadow:0 2px 10px rgba(0,0,0,0.1);}";
echo ".success{color:#28a745;} .error{color:#dc3545;} .info{color:#007bff;}";
echo "</style></head><body><div class='container'>";

echo "<h2>🔧 Quick Login Fix</h2>";

// Check database connection
if ($conn->connect_error) {
    echo "<p class='error'>❌ Database connection failed. Please check XAMPP MySQL is running.</p>";
    exit();
}

// Force recreate demo user with correct password
echo "<p class='info'>🔄 Recreating demo user...</p>";

// Delete existing demo user
$conn->query("DELETE FROM users WHERE email = '<EMAIL>'");

// Create fresh demo user
$demo_password = password_hash('demo123', PASSWORD_DEFAULT);
$stmt = $conn->prepare("INSERT INTO users (name, email, password, role, theme) VALUES (?, ?, ?, ?, ?)");
$name = "Demo User";
$email = "<EMAIL>";
$role = "admin";
$theme = "light";

if ($stmt && $stmt->bind_param("sssss", $name, $email, $demo_password, $role, $theme)) {
    if ($stmt->execute()) {
        echo "<p class='success'>✅ Demo user recreated successfully!</p>";
        
        // Test the password immediately
        $test_stmt = $conn->prepare("SELECT * FROM users WHERE email = ?");
        $test_stmt->bind_param("s", $email);
        $test_stmt->execute();
        $result = $test_stmt->get_result();
        $user = $result->fetch_assoc();
        
        if ($user && password_verify('demo123', $user['password'])) {
            echo "<p class='success'>✅ Password verification test: PASSED</p>";
            echo "<p class='success'>✅ Login should work now!</p>";
        } else {
            echo "<p class='error'>❌ Password verification test: FAILED</p>";
            echo "<p class='error'>There might be a PHP configuration issue.</p>";
        }
    } else {
        echo "<p class='error'>❌ Error creating demo user: " . $stmt->error . "</p>";
    }
} else {
    echo "<p class='error'>❌ Database error: " . $conn->error . "</p>";
}

echo "<br><div style='background:#e8f5e8;padding:15px;border-radius:8px;'>";
echo "<h3>✅ Login Credentials:</h3>";
echo "<p><strong>Email:</strong> <EMAIL></p>";
echo "<p><strong>Password:</strong> demo123</p>";
echo "<p><a href='login.php' style='background:#667eea;color:white;padding:10px 20px;text-decoration:none;border-radius:5px;'>🚀 Try Login Now</a></p>";
echo "</div>";

// Show current users for debugging
echo "<br><h3>👥 Current Users in Database:</h3>";
$users_result = $conn->query("SELECT id, name, email, role, theme FROM users");
if ($users_result && $users_result->num_rows > 0) {
    echo "<table border='1' style='border-collapse:collapse;width:100%;'>";
    echo "<tr style='background:#f8f9fa;'><th style='padding:8px;'>ID</th><th style='padding:8px;'>Name</th><th style='padding:8px;'>Email</th><th style='padding:8px;'>Role</th><th style='padding:8px;'>Theme</th></tr>";
    while ($user = $users_result->fetch_assoc()) {
        echo "<tr>";
        echo "<td style='padding:8px;'>" . $user['id'] . "</td>";
        echo "<td style='padding:8px;'>" . $user['name'] . "</td>";
        echo "<td style='padding:8px;'>" . $user['email'] . "</td>";
        echo "<td style='padding:8px;'>" . $user['role'] . "</td>";
        echo "<td style='padding:8px;'>" . $user['theme'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "<p class='error'>❌ No users found in database</p>";
}

echo "</div></body></html>";

$conn->close();
?>
