<?php
session_start();
include 'db.php';

header('Content-Type: application/json');

if (!isset($_SESSION['user'])) {
    echo json_encode(['success' => false, 'message' => 'User not logged in']);
    exit();
}

$user = $_SESSION['user'];
$input = json_decode(file_get_contents('php://input'), true);

if (!$input) {
    echo json_encode(['success' => false, 'message' => 'Invalid input data']);
    exit();
}

// Validate and sanitize input
$primary_color = $input['primary_color'] ?? '#ff6b6b';
$secondary_color = $input['secondary_color'] ?? '#ffa8a8';
$blur_intensity = max(5, min(50, intval($input['blur_intensity'] ?? 20)));
$border_radius = max(5, min(50, intval($input['border_radius'] ?? 20)));
$animation_speed = max(0.5, min(3, floatval($input['animation_speed'] ?? 1)));

// Validate hex colors
if (!preg_match('/^#[a-f0-9]{6}$/i', $primary_color)) {
    $primary_color = '#ff6b6b';
}
if (!preg_match('/^#[a-f0-9]{6}$/i', $secondary_color)) {
    $secondary_color = '#ffa8a8';
}

try {
    // Check if user customizations already exist
    $stmt = $conn->prepare("SELECT id FROM user_customizations WHERE user_id = ?");
    $stmt->bind_param("i", $user['id']);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows > 0) {
        // Update existing customizations
        $stmt = $conn->prepare("UPDATE user_customizations SET 
            primary_color = ?, 
            secondary_color = ?, 
            blur_intensity = ?, 
            border_radius = ?, 
            animation_speed = ?, 
            updated_at = NOW() 
            WHERE user_id = ?");
        $stmt->bind_param("ssiidd", $primary_color, $secondary_color, $blur_intensity, $border_radius, $animation_speed, $user['id']);
    } else {
        // Insert new customizations
        $stmt = $conn->prepare("INSERT INTO user_customizations 
            (user_id, primary_color, secondary_color, blur_intensity, border_radius, animation_speed, created_at, updated_at) 
            VALUES (?, ?, ?, ?, ?, ?, NOW(), NOW())");
        $stmt->bind_param("issiid", $user['id'], $primary_color, $secondary_color, $blur_intensity, $border_radius, $animation_speed);
    }
    
    if ($stmt->execute()) {
        echo json_encode(['success' => true, 'message' => 'Customizations saved successfully']);
    } else {
        echo json_encode(['success' => false, 'message' => 'Database error: ' . $stmt->error]);
    }
} catch (Exception $e) {
    echo json_encode(['success' => false, 'message' => 'Error: ' . $e->getMessage()]);
}
?>
