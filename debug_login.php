<?php
include 'db.php';

echo "<h2>🔍 Login Debug Information</h2>";

// Check if database connection works
if ($conn->connect_error) {
    echo "❌ Database connection failed: " . $conn->connect_error . "<br>";
    exit();
} else {
    echo "✅ Database connection successful<br>";
}

// Check if users table exists
$result = $conn->query("SHOW TABLES LIKE 'users'");
if ($result->num_rows > 0) {
    echo "✅ Users table exists<br>";
    
    // Check if there are any users
    $users_result = $conn->query("SELECT id, name, email, role, theme FROM users");
    if ($users_result->num_rows > 0) {
        echo "✅ Found " . $users_result->num_rows . " users in database:<br>";
        echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
        echo "<tr><th>ID</th><th>Name</th><th>Email</th><th>Role</th><th>Theme</th></tr>";
        while ($user = $users_result->fetch_assoc()) {
            echo "<tr>";
            echo "<td>" . $user['id'] . "</td>";
            echo "<td>" . $user['name'] . "</td>";
            echo "<td>" . $user['email'] . "</td>";
            echo "<td>" . $user['role'] . "</td>";
            echo "<td>" . $user['theme'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "❌ No users found in database<br>";
        echo "👉 <a href='setup_database.php'>Run Setup Script</a> to create demo users<br>";
    }
} else {
    echo "❌ Users table does not exist<br>";
    echo "👉 <a href='setup_database.php'>Run Setup Script</a> to create tables and users<br>";
}

// Test password verification for demo user
echo "<br><h3>🔐 Password Test</h3>";
$test_email = "<EMAIL>";
$test_password = "demo123";

$stmt = $conn->prepare("SELECT * FROM users WHERE email = ?");
if ($stmt) {
    $stmt->bind_param("s", $test_email);
    $stmt->execute();
    $result = $stmt->get_result();
    $user = $result->fetch_assoc();
    
    if ($user) {
        echo "✅ Found user: " . $user['name'] . " (" . $user['email'] . ")<br>";
        echo "🔑 Stored password hash: " . substr($user['password'], 0, 20) . "...<br>";
        
        if (password_verify($test_password, $user['password'])) {
            echo "✅ Password verification successful for 'demo123'<br>";
        } else {
            echo "❌ Password verification failed for 'demo123'<br>";
            echo "🔧 This means the password hash is incorrect<br>";
        }
    } else {
        echo "❌ Demo user not found<br>";
    }
} else {
    echo "❌ Database query failed: " . $conn->error . "<br>";
}

echo "<br><h3>📋 Next Steps:</h3>";
echo "<ol>";
echo "<li>If tables don't exist: <a href='setup_database.php'>Run Setup Script</a></li>";
echo "<li>If users don't exist: <a href='setup_database.php'>Run Setup Script</a></li>";
echo "<li>If password verification fails: <a href='setup_database.php'>Run Setup Script</a> to recreate users</li>";
echo "<li>After setup: <a href='login.php'>Try Login Again</a></li>";
echo "</ol>";

$conn->close();
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
table { border-collapse: collapse; }
th, td { padding: 8px 12px; border: 1px solid #ddd; }
th { background-color: #f5f5f5; }
a { color: #007bff; text-decoration: none; }
a:hover { text-decoration: underline; }
</style>
