<?php
include 'db.php';

echo "<h2>Setting up Glassmorphism Ecommerce Database...</h2>";

// Create users table if it doesn't exist
$sql_users = "CREATE TABLE IF NOT EXISTS users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    role VARCHAR(50) DEFAULT 'user',
    theme VARCHAR(20) DEFAULT 'light',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
)";

if ($conn->query($sql_users) === TRUE) {
    echo "✅ Users table created successfully<br>";
} else {
    echo "❌ Error creating users table: " . $conn->error . "<br>";
}

// Create user_customizations table
$sql_customizations = "CREATE TABLE IF NOT EXISTS user_customizations (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    primary_color VARCHAR(7) DEFAULT '#ff6b6b',
    secondary_color VARCHAR(7) DEFAULT '#ffa8a8',
    blur_intensity INT DEFAULT 20,
    border_radius INT DEFAULT 20,
    animation_speed DECIMAL(3,1) DEFAULT 1.0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
)";

if ($conn->query($sql_customizations) === TRUE) {
    echo "✅ User customizations table created successfully<br>";
} else {
    echo "❌ Error creating user_customizations table: " . $conn->error . "<br>";
}

// Check if demo user exists
$check_user = $conn->query("SELECT id FROM users WHERE email = '<EMAIL>'");

if ($check_user->num_rows == 0) {
    // Create demo user
    $demo_password = password_hash('demo123', PASSWORD_DEFAULT);
    $stmt = $conn->prepare("INSERT INTO users (name, email, password, role, theme) VALUES (?, ?, ?, ?, ?)");
    $name = "Demo User";
    $email = "<EMAIL>";
    $role = "admin";
    $theme = "light";
    $stmt->bind_param("sssss", $name, $email, $demo_password, $role, $theme);
    
    if ($stmt->execute()) {
        echo "✅ Demo user created successfully<br>";
        echo "📧 Email: <EMAIL><br>";
        echo "🔒 Password: demo123<br>";
    } else {
        echo "❌ Error creating demo user: " . $stmt->error . "<br>";
    }
} else {
    echo "ℹ️ Demo user already exists<br>";
}

// Create additional demo users with different themes
$demo_users = [
    ['name' => 'Alice Johnson', 'email' => '<EMAIL>', 'role' => 'user', 'theme' => 'dark'],
    ['name' => 'Bob Smith', 'email' => '<EMAIL>', 'role' => 'user', 'theme' => 'custom'],
    ['name' => 'Carol Davis', 'email' => '<EMAIL>', 'role' => 'manager', 'theme' => 'light']
];

foreach ($demo_users as $user_data) {
    $check_existing = $conn->prepare("SELECT id FROM users WHERE email = ?");
    $check_existing->bind_param("s", $user_data['email']);
    $check_existing->execute();
    $result = $check_existing->get_result();
    
    if ($result->num_rows == 0) {
        $password = password_hash('demo123', PASSWORD_DEFAULT);
        $stmt = $conn->prepare("INSERT INTO users (name, email, password, role, theme) VALUES (?, ?, ?, ?, ?)");
        $stmt->bind_param("sssss", $user_data['name'], $user_data['email'], $password, $user_data['role'], $user_data['theme']);
        
        if ($stmt->execute()) {
            echo "✅ Created user: " . $user_data['name'] . " (" . $user_data['email'] . ")<br>";
            
            // Add custom theme settings for Bob
            if ($user_data['theme'] === 'custom') {
                $user_id = $conn->insert_id;
                $custom_stmt = $conn->prepare("INSERT INTO user_customizations (user_id, primary_color, secondary_color, blur_intensity, border_radius, animation_speed) VALUES (?, ?, ?, ?, ?, ?)");
                $primary = '#4fd1c7';
                $secondary = '#667eea';
                $blur = 25;
                $radius = 15;
                $speed = 1.5;
                $custom_stmt->bind_param("issiid", $user_id, $primary, $secondary, $blur, $radius, $speed);
                $custom_stmt->execute();
                echo "✅ Added custom theme settings for " . $user_data['name'] . "<br>";
            }
        }
    }
}

echo "<br><h3>🎉 Database setup complete!</h3>";
echo "<p>You can now log in with any of these accounts:</p>";
echo "<ul>";
echo "<li><strong>Admin:</strong> <EMAIL> / demo123</li>";
echo "<li><strong>User (Dark theme):</strong> <EMAIL> / demo123</li>";
echo "<li><strong>User (Custom theme):</strong> <EMAIL> / demo123</li>";
echo "<li><strong>Manager:</strong> <EMAIL> / demo123</li>";
echo "</ul>";
echo "<p><a href='login.php' style='color: #667eea; text-decoration: none; font-weight: bold;'>→ Go to Login Page</a></p>";

$conn->close();
?>
