<?php
include 'db.php';

echo "<!DOCTYPE html><html><head><title>Database Setup</title>";
echo "<style>body{font-family:Arial,sans-serif;margin:20px;background:#f5f5f5;}";
echo ".container{max-width:800px;margin:0 auto;background:white;padding:20px;border-radius:10px;box-shadow:0 2px 10px rgba(0,0,0,0.1);}";
echo ".success{color:#28a745;} .error{color:#dc3545;} .info{color:#007bff;}";
echo "</style></head><body><div class='container'>";

echo "<h2>🔧 Setting up Glassmorphism Ecommerce Database...</h2>";

// First, create the database if it doesn't exist
$conn_no_db = new mysqli("localhost", "root", "");
if ($conn_no_db->connect_error) {
    echo "<p class='error'>❌ Cannot connect to MySQL: " . $conn_no_db->connect_error . "</p>";
    echo "<p>Please make sure XAMPP MySQL is running!</p>";
    exit();
}

$result = $conn_no_db->query("CREATE DATABASE IF NOT EXISTS ecommerce_db");
if ($result) {
    echo "<p class='success'>✅ Database 'ecommerce_db' ready</p>";
} else {
    echo "<p class='error'>❌ Error creating database: " . $conn_no_db->error . "</p>";
}
$conn_no_db->close();

// Now connect to the specific database
if ($conn->connect_error) {
    echo "<p class='error'>❌ Database connection failed: " . $conn->connect_error . "</p>";
    exit();
} else {
    echo "<p class='success'>✅ Connected to ecommerce_db</p>";
}

// Create users table if it doesn't exist
$sql_users = "CREATE TABLE IF NOT EXISTS users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    role VARCHAR(50) DEFAULT 'user',
    theme VARCHAR(20) DEFAULT 'light',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
)";

if ($conn->query($sql_users) === TRUE) {
    echo "<p class='success'>✅ Users table created successfully</p>";
} else {
    echo "<p class='error'>❌ Error creating users table: " . $conn->error . "</p>";
}

// Create user_customizations table
$sql_customizations = "CREATE TABLE IF NOT EXISTS user_customizations (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    primary_color VARCHAR(7) DEFAULT '#ff6b6b',
    secondary_color VARCHAR(7) DEFAULT '#ffa8a8',
    blur_intensity INT DEFAULT 20,
    border_radius INT DEFAULT 20,
    animation_speed DECIMAL(3,1) DEFAULT 1.0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_user_id (user_id)
)";

if ($conn->query($sql_customizations) === TRUE) {
    echo "<p class='success'>✅ User customizations table created successfully</p>";
} else {
    echo "<p class='error'>❌ Error creating user_customizations table: " . $conn->error . "</p>";
}

// Delete existing demo user if exists (to recreate with fresh password)
$conn->query("DELETE FROM users WHERE email = '<EMAIL>'");

// Create demo user with proper password hashing
$demo_password = password_hash('demo123', PASSWORD_DEFAULT);
$stmt = $conn->prepare("INSERT INTO users (name, email, password, role, theme) VALUES (?, ?, ?, ?, ?)");
$name = "Demo User";
$email = "<EMAIL>";
$role = "admin";
$theme = "light";

if ($stmt && $stmt->bind_param("sssss", $name, $email, $demo_password, $role, $theme)) {
    if ($stmt->execute()) {
        echo "<p class='success'>✅ Demo user created successfully</p>";
        echo "<p class='info'>📧 Email: <EMAIL></p>";
        echo "<p class='info'>🔒 Password: demo123</p>";

        // Test the password immediately
        $test_stmt = $conn->prepare("SELECT password FROM users WHERE email = ?");
        $test_stmt->bind_param("s", $email);
        $test_stmt->execute();
        $result = $test_stmt->get_result();
        $user = $result->fetch_assoc();

        if ($user && password_verify('demo123', $user['password'])) {
            echo "<p class='success'>✅ Password verification test passed</p>";
        } else {
            echo "<p class='error'>❌ Password verification test failed</p>";
        }
    } else {
        echo "<p class='error'>❌ Error creating demo user: " . $stmt->error . "</p>";
    }
} else {
    echo "<p class='error'>❌ Error preparing demo user statement: " . $conn->error . "</p>";
}

// Create additional demo users with different themes
$demo_users = [
    ['name' => 'Alice Johnson', 'email' => '<EMAIL>', 'role' => 'user', 'theme' => 'dark'],
    ['name' => 'Bob Smith', 'email' => '<EMAIL>', 'role' => 'user', 'theme' => 'custom'],
    ['name' => 'Carol Davis', 'email' => '<EMAIL>', 'role' => 'manager', 'theme' => 'light']
];

// Delete existing demo users first
foreach ($demo_users as $user_data) {
    $conn->query("DELETE FROM users WHERE email = '" . $user_data['email'] . "'");
}

foreach ($demo_users as $user_data) {
    $password = password_hash('demo123', PASSWORD_DEFAULT);
    $stmt = $conn->prepare("INSERT INTO users (name, email, password, role, theme) VALUES (?, ?, ?, ?, ?)");

    if ($stmt && $stmt->bind_param("sssss", $user_data['name'], $user_data['email'], $password, $user_data['role'], $user_data['theme'])) {
        if ($stmt->execute()) {
            echo "<p class='success'>✅ Created user: " . $user_data['name'] . " (" . $user_data['email'] . ")</p>";

            // Add custom theme settings for Bob
            if ($user_data['theme'] === 'custom') {
                $user_id = $conn->insert_id;
                $custom_stmt = $conn->prepare("INSERT INTO user_customizations (user_id, primary_color, secondary_color, blur_intensity, border_radius, animation_speed) VALUES (?, ?, ?, ?, ?, ?)");
                $primary = '#4fd1c7';
                $secondary = '#667eea';
                $blur = 25;
                $radius = 15;
                $speed = 1.5;
                if ($custom_stmt && $custom_stmt->bind_param("issiid", $user_id, $primary, $secondary, $blur, $radius, $speed)) {
                    $custom_stmt->execute();
                    echo "<p class='success'>✅ Added custom theme settings for " . $user_data['name'] . "</p>";
                }
            }
        } else {
            echo "<p class='error'>❌ Error creating user " . $user_data['name'] . ": " . $stmt->error . "</p>";
        }
    }
}

echo "<br><div style='background:#e8f5e8;padding:15px;border-radius:8px;border-left:4px solid #28a745;'>";
echo "<h3>🎉 Database setup complete!</h3>";
echo "<p><strong>You can now log in with any of these accounts:</strong></p>";
echo "<ul>";
echo "<li><strong>Admin:</strong> <EMAIL> / demo123</li>";
echo "<li><strong>User (Dark theme):</strong> <EMAIL> / demo123</li>";
echo "<li><strong>User (Custom theme):</strong> <EMAIL> / demo123</li>";
echo "<li><strong>Manager:</strong> <EMAIL> / demo123</li>";
echo "</ul>";
echo "<p><a href='login.php' style='background:#667eea;color:white;padding:10px 20px;text-decoration:none;border-radius:5px;display:inline-block;margin-top:10px;'>🚀 Go to Login Page</a></p>";
echo "</div>";

// Final verification
echo "<br><h3>🔍 Final Verification:</h3>";
$verify_result = $conn->query("SELECT COUNT(*) as count FROM users");
$count = $verify_result->fetch_assoc()['count'];
echo "<p class='info'>Total users created: " . $count . "</p>";

echo "</div></body></html>";

$conn->close();
?>
