/* Glassmorphism Custom Theme - User Customizable */
:root {
    /* Default custom theme values - can be overridden by user preferences */
    --primary-bg: linear-gradient(135deg, #ff9a9e 0%, #fecfef 50%, #fecfef 100%);
    --glass-bg: rgba(255, 255, 255, 0.2);
    --glass-border: rgba(255, 255, 255, 0.3);
    --text-primary: #2d3748;
    --text-secondary: #4a5568;
    --accent-color: #ff6b6b;
    --shadow-color: rgba(0, 0, 0, 0.15);
    --hover-bg: rgba(255, 255, 255, 0.3);
    --input-bg: rgba(255, 255, 255, 0.15);
    --success-color: #51cf66;
    --warning-color: #ffd43b;
    --error-color: #ff6b6b;

    /* User customizable properties */
    --user-primary-color: var(--accent-color);
    --user-secondary-color: #ffa8a8;
    --user-blur-intensity: 20px;
    --user-border-radius: 20px;
    --user-animation-speed: 1s;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: var(--primary-bg);
    color: var(--text-primary);
    min-height: 100vh;
    overflow-x: hidden;
}

/* Animated background particles with customizable speed */
body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="20" cy="20" r="2" fill="rgba(255,107,107,0.1)"/><circle cx="80" cy="40" r="1.5" fill="rgba(255,107,107,0.08)"/><circle cx="40" cy="80" r="1" fill="rgba(255,107,107,0.06)"/></svg>');
    animation: float calc(20s / var(--user-animation-speed)) infinite linear;
    pointer-events: none;
    z-index: -1;
}

@keyframes float {
    0% { transform: translateY(0px) rotate(0deg); }
    100% { transform: translateY(-100vh) rotate(360deg); }
}

/* Glass container with customizable blur and radius */
.glass-container {
    background: var(--glass-bg);
    backdrop-filter: blur(var(--user-blur-intensity));
    -webkit-backdrop-filter: blur(var(--user-blur-intensity));
    border: 1px solid var(--glass-border);
    border-radius: var(--user-border-radius);
    padding: 2rem;
    margin: 2rem auto;
    max-width: 800px;
    box-shadow: 0 8px 32px var(--shadow-color);
    transition: all 0.3s ease;
}

.glass-container:hover {
    transform: translateY(-5px);
    box-shadow: 0 12px 40px var(--shadow-color);
}

/* Typography */
h1 {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
    background: linear-gradient(135deg, var(--user-primary-color), var(--user-secondary-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-align: center;
}

h2 {
    font-size: 1.8rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: var(--text-primary);
}

p {
    font-size: 1.1rem;
    line-height: 1.6;
    color: var(--text-secondary);
    margin-bottom: 1rem;
}

/* Buttons */
.btn {
    background: var(--glass-bg);
    backdrop-filter: blur(10px);
    border: 1px solid var(--glass-border);
    border-radius: calc(var(--user-border-radius) * 0.6);
    padding: 0.75rem 1.5rem;
    color: var(--text-primary);
    text-decoration: none;
    font-weight: 500;
    transition: all 0.3s ease;
    cursor: pointer;
    display: inline-block;
    margin: 0.5rem;
}

.btn:hover {
    background: var(--hover-bg);
    transform: translateY(-2px);
    box-shadow: 0 4px 20px var(--shadow-color);
}

.btn-primary {
    background: linear-gradient(135deg, var(--user-primary-color), var(--user-secondary-color));
    color: white;
    border: none;
}

.btn-primary:hover {
    background: linear-gradient(135deg, var(--user-secondary-color), var(--user-primary-color));
}

/* Form elements */
select, input {
    background: var(--input-bg);
    backdrop-filter: blur(10px);
    border: 1px solid var(--glass-border);
    border-radius: calc(var(--user-border-radius) * 0.6);
    padding: 0.75rem 1rem;
    color: var(--text-primary);
    font-size: 1rem;
    transition: all 0.3s ease;
    width: 100%;
    margin-bottom: 1rem;
}

select:focus, input:focus {
    outline: none;
    border-color: var(--user-primary-color);
    box-shadow: 0 0 0 3px rgba(255, 107, 107, 0.2);
}

/* Theme selector */
.theme-selector {
    display: flex;
    gap: 1rem;
    justify-content: center;
    margin: 2rem 0;
    flex-wrap: wrap;
}

.theme-option {
    background: var(--glass-bg);
    backdrop-filter: blur(10px);
    border: 2px solid var(--glass-border);
    border-radius: calc(var(--user-border-radius) * 0.75);
    padding: 1rem;
    cursor: pointer;
    transition: all 0.3s ease;
    text-align: center;
    min-width: 120px;
}

.theme-option:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 25px var(--shadow-color);
}

.theme-option.active {
    border-color: var(--user-primary-color);
    background: var(--hover-bg);
}

/* Navigation */
.nav {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 2rem;
    background: var(--glass-bg);
    backdrop-filter: blur(var(--user-blur-intensity));
    border-bottom: 1px solid var(--glass-border);
    margin-bottom: 2rem;
}

.nav-brand {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--user-primary-color);
}

.nav-links {
    display: flex;
    gap: 1rem;
    align-items: center;
}

/* Customization Panel */
.customization-panel {
    background: var(--glass-bg);
    backdrop-filter: blur(var(--user-blur-intensity));
    border: 1px solid var(--glass-border);
    border-radius: var(--user-border-radius);
    padding: 1.5rem;
    margin: 1rem 0;
}

.color-picker-group {
    display: flex;
    gap: 1rem;
    margin: 1rem 0;
    flex-wrap: wrap;
}

.color-picker {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
}

.color-picker input[type="color"] {
    width: 50px;
    height: 50px;
    border: none;
    border-radius: 50%;
    cursor: pointer;
    transition: all 0.3s ease;
}

.color-picker input[type="color"]:hover {
    transform: scale(1.1);
}

.slider-group {
    margin: 1rem 0;
}

.slider-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
}

.slider-group input[type="range"] {
    width: 100%;
    margin-bottom: 0.5rem;
}

/* Responsive design */
@media (max-width: 768px) {
    .glass-container {
        margin: 1rem;
        padding: 1.5rem;
    }

    h1 {
        font-size: 2rem;
    }

    .theme-selector {
        flex-direction: column;
        align-items: center;
    }

    .nav {
        flex-direction: column;
        gap: 1rem;
        padding: 1rem;
    }

    .color-picker-group {
        justify-content: center;
    }
}