# ✨ Glassmorphism Ecommerce Theme Switch

A beautiful, modern ecommerce dashboard with glassmorphism design and per-user theme customization.

## 🎨 Features

### Glassmorphism Design
- **Frosted glass effect** with backdrop blur
- **Translucent containers** with subtle borders
- **Smooth animations** and hover effects
- **Gradient backgrounds** with floating particles
- **Modern typography** with gradient text effects

### Theme System
- **Light Theme**: Clean and bright with blue-purple gradients
- **Dark Theme**: Elegant dark mode with teal accents
- **Custom Theme**: Fully customizable by each user

### User Customization (Custom Theme)
- **Color Picker**: Choose primary and secondary colors
- **Blur Intensity**: Adjust glass blur effect (5-50px)
- **Border Radius**: Customize corner roundness (5-50px)
- **Animation Speed**: Control particle animation speed (0.5x-3x)
- **Real-time Preview**: See changes instantly
- **Persistent Settings**: Customizations saved per user

## 🚀 Quick Start

1. **Setup Database**:
   ```
   http://localhost/ecommerce_theme_switch/setup_database.php
   ```

2. **Login with Demo Account**:
   - Email: `<EMAIL>`
   - Password: `demo123`

3. **Try Different Themes**:
   - Switch between Light, Dark, and Custom themes
   - In Custom theme, use the customization panel to personalize your experience

## 🎯 Demo Accounts

| Email | Password | Role | Default Theme |
|-------|----------|------|---------------|
| <EMAIL> | demo123 | Admin | Light |
| <EMAIL> | demo123 | User | Dark |
| <EMAIL> | demo123 | User | Custom (pre-configured) |
| <EMAIL> | demo123 | Manager | Light |

## 🛠️ Technical Details

### CSS Architecture
- **CSS Custom Properties** for dynamic theming
- **Backdrop-filter** for glassmorphism effects
- **CSS Grid & Flexbox** for responsive layouts
- **CSS Animations** for smooth interactions

### Database Schema
```sql
-- Users table
users (id, name, email, password, role, theme, created_at)

-- User customizations table
user_customizations (
    id, user_id, primary_color, secondary_color, 
    blur_intensity, border_radius, animation_speed,
    created_at, updated_at
)
```

### File Structure
```
├── css/
│   ├── light.css      # Light glassmorphism theme
│   ├── dark.css       # Dark glassmorphism theme
│   └── custom.css     # Customizable glassmorphism theme
├── dashboard.php      # Main dashboard with theme switching
├── login.php          # Glassmorphism login page
├── save_customizations.php  # API for saving user preferences
├── update_theme.php   # Theme switching endpoint
├── setup_database.php # Database setup script
└── README.md         # This file
```

## 🎨 Customization Guide

### Adding New Themes
1. Create a new CSS file in the `css/` folder
2. Follow the glassmorphism pattern with CSS custom properties
3. Add the theme option to the dashboard theme selector

### Modifying Glass Effects
Key CSS properties for glassmorphism:
```css
.glass-container {
    background: rgba(255, 255, 255, 0.25);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.18);
    border-radius: 20px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}
```

### Custom Properties
Each theme uses CSS custom properties for easy customization:
```css
:root {
    --primary-bg: /* Background gradient */
    --glass-bg: /* Glass background */
    --glass-border: /* Glass border */
    --text-primary: /* Primary text color */
    --accent-color: /* Accent color */
    /* ... more properties */
}
```

## 🌟 Browser Support

- **Chrome/Edge**: Full support with backdrop-filter
- **Firefox**: Partial support (fallback styles included)
- **Safari**: Full support
- **Mobile**: Responsive design with touch-friendly interactions

## 📱 Responsive Design

- **Desktop**: Full glassmorphism experience
- **Tablet**: Optimized layouts with maintained glass effects
- **Mobile**: Simplified layouts, preserved visual hierarchy

## 🔧 Development

### Requirements
- PHP 7.4+
- MySQL 5.7+
- Modern web browser with backdrop-filter support

### Local Development
1. Place files in your web server directory
2. Configure database connection in `db.php`
3. Run `setup_database.php` to initialize
4. Access `login.php` to start

## 🎉 What's Next?

This glassmorphism design system can be extended with:
- **Product catalog** with glass cards
- **Shopping cart** with glassmorphism UI
- **User profiles** with avatar customization
- **Admin panel** with glass data tables
- **Mobile app** with native glass effects

Enjoy your beautiful glassmorphism ecommerce experience! ✨
